import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomerStatisticsService } from '../service/customer-statistics.service';
import {
  CustomerStatisticsQueryDTO,
  CustomerRegistrationTrendDTO,
  CustomerGenderDistributionDTO,
  CustomerMemberStatusDistributionDTO,
} from '../dto/customer-statistics.dto';

@Controller('/customer-statistics')
export class CustomerStatisticsController {
  @Inject()
  ctx: Context;

  @Inject()
  customerStatisticsService: CustomerStatisticsService;

  /**
   * 获取用户概览统计
   * GET /customer-statistics/overview
   */
  @Get('/overview', { summary: '获取用户概览统计' })
  async getOverview(@Query() query: CustomerStatisticsQueryDTO) {
    const { startDate, endDate } = query;
    return await this.customerStatisticsService.getCustomerOverview(
      startDate,
      endDate
    );
  }

  /**
   * 获取用户注册趋势统计
   * GET /customer-statistics/registration-trend
   */
  @Get('/registration-trend', { summary: '获取用户注册趋势统计' })
  async getRegistrationTrend(@Query() query: CustomerRegistrationTrendDTO) {
    const { startDate, endDate, periodType = 'day' } = query;

    if (!startDate || !endDate) {
      throw new Error('开始日期和结束日期不能为空');
    }

    return await this.customerStatisticsService.getRegistrationTrend(
      startDate,
      endDate,
      periodType
    );
  }

  /**
   * 获取用户性别分布统计
   * GET /customer-statistics/gender-distribution
   */
  @Get('/gender-distribution', { summary: '获取用户性别分布统计' })
  async getGenderDistribution(@Query() query: CustomerGenderDistributionDTO) {
    const { startDate, endDate } = query;
    return await this.customerStatisticsService.getGenderDistribution(
      startDate,
      endDate
    );
  }

  /**
   * 获取用户会员状态分布统计
   * GET /customer-statistics/member-status-distribution
   */
  @Get('/member-status-distribution', { summary: '获取用户会员状态分布统计' })
  async getMemberStatusDistribution(@Query() query: CustomerMemberStatusDistributionDTO) {
    const { startDate, endDate } = query;
    return await this.customerStatisticsService.getMemberStatusDistribution(
      startDate,
      endDate
    );
  }

  /**
   * 获取用户统计汇总
   * GET /customer-statistics/summary
   */
  @Get('/summary', { summary: '获取用户统计汇总' })
  async getSummary(@Query() query: CustomerStatisticsQueryDTO) {
    const { startDate, endDate } = query;
    
    // 获取概览数据
    const overview = await this.customerStatisticsService.getCustomerOverview(
      startDate,
      endDate
    );
    
    // 获取性别分布
    const genderDistribution = await this.customerStatisticsService.getGenderDistribution(
      startDate,
      endDate
    );
    
    // 获取会员状态分布
    const memberStatusDistribution = await this.customerStatisticsService.getMemberStatusDistribution(
      startDate,
      endDate
    );

    return {
      overview: {
        totalUsers: overview.totalUsers,
        todayNewUsers: overview.todayNewUsers,
        monthNewUsers: overview.monthNewUsers,
      },
      genderDistribution,
      memberStatusDistribution,
    };
  }

  /**
   * 获取用户增长趋势（最近30天）
   * GET /customer-statistics/growth-trend
   */
  @Get('/growth-trend', { summary: '获取用户增长趋势' })
  async getGrowthTrend() {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    const trendData = await this.customerStatisticsService.getRegistrationTrend(
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0],
      'day'
    );

    return {
      period: '最近30天',
      data: trendData,
    };
  }

  /**
   * 获取用户活跃度统计
   * GET /customer-statistics/activity
   */
  @Get('/activity', { summary: '获取用户活跃度统计' })
  async getActivityStats(@Query() query: CustomerStatisticsQueryDTO) {
    const { startDate, endDate } = query;
    
    // 这里可以扩展用户活跃度相关的统计
    // 比如最后登录时间分布、订单活跃度等
    const overview = await this.customerStatisticsService.getCustomerOverview(
      startDate,
      endDate
    );

    return {
      totalUsers: overview.totalUsers,
      activeUsers: overview.totalUsers, // 暂时使用总用户数，后续可以根据最后登录时间等条件优化
      inactiveUsers: 0,
      activityRate: 100, // 活跃率，后续可以根据实际业务逻辑计算
    };
  }

  /**
   * 获取用户地域分布统计
   * GET /customer-statistics/region-distribution
   */
  @Get('/region-distribution', { summary: '获取用户地域分布统计' })
  async getRegionDistribution(@Query() query: CustomerStatisticsQueryDTO) {
    // 这里可以根据用户地址信息进行地域分布统计
    // 暂时返回空数据，后续可以扩展
    return {
      message: '地域分布统计功能待开发',
      data: [],
    };
  }
}
