import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Op, fn, col } from 'sequelize';
import { Customer } from '../entity';

@Provide()
export class CustomerStatisticsService {
  @Inject()
  ctx: Context;

  /**
   * 获取用户概览统计
   */
  async getCustomerOverview(startDate?: string, endDate?: string) {
    console.log('=== 开始用户概览统计 ===');
    console.log('输入参数:', { startDate, endDate });

    try {
      // 先测试不加任何条件的查询
      const allUsers = await Customer.count();
      console.log('数据库中所有用户数:', allUsers);
    } catch (error) {
      console.error('查询所有用户数据时出错:', error);
    }

    const whereCondition: any = { status: 1 }; // 只统计启用状态的用户

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 总用户数
    const totalUsers = await Customer.count({ where: whereCondition });

    // 调试信息
    console.log('查询条件:', whereCondition);
    console.log('启用状态用户数:', totalUsers);

    // 今日新增用户
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayNewUsers = await Customer.count({
      where: {
        status: 1,
        createdAt: {
          [Op.between]: [today, tomorrow],
        },
      },
    });

    // 本月新增用户
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 1);

    const monthNewUsers = await Customer.count({
      where: {
        status: 1,
        createdAt: {
          [Op.between]: [monthStart, monthEnd],
        },
      },
    });

    // 性别分布统计
    const genderStats = await Customer.findAll({
      where: whereCondition,
      attributes: ['gender', [fn('COUNT', col('id')), 'count']],
      group: ['gender'],
      raw: true,
    });

    // 会员状态分布统计
    const memberStatusStats = await Customer.findAll({
      where: whereCondition,
      attributes: ['memberStatus', [fn('COUNT', col('id')), 'count']],
      group: ['memberStatus'],
      raw: true,
    });

    // 计算性别比例
    const genderDistribution = this.calculateGenderDistribution(
      genderStats as any[],
      totalUsers
    );

    // 计算会员状态比例
    const memberStatusDistribution = this.calculateMemberStatusDistribution(
      memberStatusStats as any[],
      totalUsers
    );

    return {
      totalUsers,
      todayNewUsers,
      monthNewUsers,
      genderDistribution,
      memberStatusDistribution,
    };
  }

  /**
   * 获取用户注册趋势统计
   */
  async getRegistrationTrend(
    startDate: string,
    endDate: string,
    periodType: 'day' | 'week' | 'month' = 'day'
  ) {
    const whereCondition: any = {
      status: 1,
      createdAt: {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      },
    };

    let dateFormat: string;

    switch (periodType) {
      case 'day':
        dateFormat = '%Y-%m-%d';
        break;
      case 'week':
        dateFormat = '%Y-%u';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        break;
      default:
        dateFormat = '%Y-%m-%d';
    }

    const trendData = await Customer.findAll({
      where: whereCondition,
      attributes: [
        [fn('DATE_FORMAT', col('createdAt'), dateFormat), 'period'],
        [fn('COUNT', col('id')), 'count'],
      ],
      group: [fn('DATE_FORMAT', col('createdAt'), dateFormat)],
      order: [[fn('DATE_FORMAT', col('createdAt'), dateFormat), 'ASC']],
      raw: true,
    });

    return trendData;
  }

  /**
   * 获取性别分布统计
   */
  async getGenderDistribution(startDate?: string, endDate?: string) {
    const whereCondition: any = { status: 1 };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    const genderStats = await Customer.findAll({
      where: whereCondition,
      attributes: ['gender', [fn('COUNT', col('id')), 'count']],
      group: ['gender'],
      raw: true,
    });

    const totalUsers = await Customer.count({ where: whereCondition });

    return this.calculateGenderDistribution(genderStats as any[], totalUsers);
  }

  /**
   * 获取会员状态分布统计
   */
  async getMemberStatusDistribution(startDate?: string, endDate?: string) {
    const whereCondition: any = { status: 1 };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    const memberStatusStats = await Customer.findAll({
      where: whereCondition,
      attributes: ['memberStatus', [fn('COUNT', col('id')), 'count']],
      group: ['memberStatus'],
      raw: true,
    });

    const totalUsers = await Customer.count({ where: whereCondition });

    return this.calculateMemberStatusDistribution(
      memberStatusStats as any[],
      totalUsers
    );
  }

  /**
   * 计算性别分布比例
   */
  private calculateGenderDistribution(genderStats: any[], totalUsers: number) {
    const genderMap = {
      0: '女',
      1: '男',
      2: '未知',
    };

    const distribution = genderStats.map(item => {
      const count = parseInt(item.count);
      const percentage =
        totalUsers > 0 ? ((count / totalUsers) * 100).toFixed(2) : '0.00';

      return {
        gender: item.gender,
        genderName: genderMap[item.gender] || '未知',
        count,
        percentage: parseFloat(percentage),
      };
    });

    // 确保所有性别都有数据
    [0, 1].forEach(gender => {
      if (!distribution.find(item => item.gender === gender)) {
        distribution.push({
          gender,
          genderName: genderMap[gender],
          count: 0,
          percentage: 0,
        });
      }
    });

    return distribution.sort((a, b) => a.gender - b.gender);
  }

  /**
   * 计算会员状态分布比例
   */
  private calculateMemberStatusDistribution(
    memberStatusStats: any[],
    totalUsers: number
  ) {
    const statusMap = {
      0: '普通会员',
      1: '权益会员',
    };

    const distribution = memberStatusStats.map(item => {
      const count = parseInt(item.count);
      const percentage =
        totalUsers > 0 ? ((count / totalUsers) * 100).toFixed(2) : '0.00';

      return {
        memberStatus: item.memberStatus,
        statusName: statusMap[item.memberStatus] || '未知',
        count,
        percentage: parseFloat(percentage),
      };
    });

    // 确保所有状态都有数据
    [0, 1].forEach(status => {
      if (!distribution.find(item => item.memberStatus === status)) {
        distribution.push({
          memberStatus: status,
          statusName: statusMap[status],
          count: 0,
          percentage: 0,
        });
      }
    });

    return distribution.sort((a, b) => a.memberStatus - b.memberStatus);
  }
}
